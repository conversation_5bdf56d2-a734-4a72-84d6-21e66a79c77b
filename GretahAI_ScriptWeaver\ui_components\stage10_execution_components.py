"""
Stage 10 Execution Components for GretahAI ScriptWeaver

Pytest execution controls, results display, performance metrics, and artifacts components.
Extracted from stage10_components.py for better maintainability.
"""

import streamlit as st
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional

# Import core dependencies
from debug_utils import debug

# Configure logging
logger = logging.getLogger("ScriptWeaver.stage10_execution_ui")


def render_script_execution_section_header():
    """
    Render the script execution section header.
    """
    st.markdown("---")
    st.markdown("## 🧪 Test Generated Script")


def render_script_info_card(script_data, target_test_case, execution_status):
    """
    Render script information card with status indicators.

    Args:
        script_data: Script data from session state
        target_test_case: Target test case information
        execution_status: Current execution status (ready, passed, failed)
    """
    filename = script_data.get('filename', 'Unknown')
    tc_id = target_test_case.get('Test Case ID', 'Unknown')
    generation_time = script_data.get('generation_timestamp', 'Unknown')

    # Format timestamp
    if generation_time != 'Unknown':
        try:
            dt = datetime.fromisoformat(generation_time.replace('Z', '+00:00'))
            generation_time = dt.strftime('%Y-%m-%d %H:%M:%S')
        except:
            pass

    # Status-based styling
    if execution_status == 'passed':
        status_color = 'success'
        status_icon = '✅'
        status_text = 'Passed'
    elif execution_status == 'failed':
        status_color = 'error'
        status_icon = '❌'
        status_text = 'Failed'
    else:
        status_color = 'info'
        status_icon = '⏳'
        status_text = 'Ready'

    # Create info card with status
    if status_color == 'success':
        st.success(f"""
        {status_icon} **Script Status: {status_text}**
        - File: {filename}
        - Target: {tc_id}
        - Generated: {generation_time}
        """)
    elif status_color == 'error':
        st.error(f"""
        {status_icon} **Script Status: {status_text}**
        - File: {filename}
        - Target: {tc_id}
        - Generated: {generation_time}
        """)
    else:
        st.info(f"""
        {status_icon} **Script Status: {status_text}**
        - File: {filename}
        - Target: {tc_id}
        - Generated: {generation_time}
        """)


def render_execution_controls_header():
    """
    Render the execution controls header.
    """
    st.markdown("### ⚡ Execution Controls")


def render_verbose_mode_checkbox(verbose_mode_key):
    """
    Render the verbose mode checkbox.

    Args:
        verbose_mode_key: Session state key for verbose mode

    Returns:
        bool: Verbose mode setting
    """
    verbose_mode = st.checkbox(
        "🔍 Verbose Output",
        value=st.session_state.get(verbose_mode_key, False),
        help="Show detailed execution logs and debug information",
        key=verbose_mode_key
    )
    return verbose_mode


def render_execution_status_indicator(execution_status):
    """
    Render the execution status indicator.

    Args:
        execution_status: Current execution status
    """
    if execution_status == 'passed':
        st.success("✅ **Last Execution: PASSED**")
    elif execution_status == 'failed':
        st.error("❌ **Last Execution: FAILED**")
    elif execution_status == 'running':
        st.info("⏳ **Execution in Progress...**")
    else:
        st.info("⏳ **Ready for Execution**")


def render_execution_action_buttons(execution_status, filename, execute_callback, clear_callback):
    """
    Render execution action buttons.

    Args:
        execution_status: Current execution status
        filename: Script filename
        execute_callback: Function to call for execution
        clear_callback: Function to call for clearing results

    Returns:
        tuple: (execute_clicked, clear_clicked)
    """
    col1, col2, col3 = st.columns([2, 1, 1])

    execute_clicked = False
    clear_clicked = False

    # Create unique keys based on filename to avoid duplicate button IDs
    execute_key = f"stage10_execute_{filename}"
    clear_key = f"stage10_clear_{filename}"
    rerun_key = f"stage10_rerun_{filename}"

    with col1:
        if execution_status == 'running':
            st.button(
                "⏳ Executing...",
                disabled=True,
                use_container_width=True,
                key=f"{execute_key}_running"
            )
        else:
            execute_clicked = st.button(
                "▶️ Execute Script",
                use_container_width=True,
                type="primary",
                help=f"Run {filename} with pytest",
                key=execute_key
            )

    with col2:
        if execution_status in ['passed', 'failed']:
            clear_clicked = st.button(
                "🗑️ Clear",
                use_container_width=True,
                type="secondary",
                help="Clear execution results",
                key=clear_key
            )
        else:
            st.button(
                "🗑️ Clear",
                disabled=True,
                use_container_width=True,
                key=f"{clear_key}_disabled"
            )

    with col3:
        if execution_status in ['passed', 'failed']:
            if st.button(
                "🔄 Re-run",
                use_container_width=True,
                type="secondary",
                key=rerun_key
            ):
                execute_clicked = True
        else:
            st.button(
                "🔄 Re-run",
                disabled=True,
                use_container_width=True,
                key=f"{rerun_key}_disabled"
            )

    return execute_clicked, clear_clicked


def render_execution_results_header():
    """
    Render the execution results header.
    """
    st.markdown("---")
    st.markdown("### 📊 Execution Results")


def render_execution_results_summary(test_results, target_test_case):
    """
    Render the execution results summary with status-based styling.

    Args:
        test_results: Test execution results
        target_test_case: Target test case information
    """
    if not test_results:
        st.info("No execution results available.")
        return

    # Extract key metrics
    exit_code = test_results.get('exit_code', -1)
    execution_time = test_results.get('execution_time', 0)
    tc_id = target_test_case.get('Test Case ID', 'Unknown')

    # Status-based display
    if exit_code == 0:
        st.success(f"""
        ✅ **Execution Successful**
        - Test Case: {tc_id}
        - Duration: {execution_time:.2f}s
        - Status: PASSED
        """)
    else:
        st.error(f"""
        ❌ **Execution Failed**
        - Test Case: {tc_id}
        - Duration: {execution_time:.2f}s
        - Exit Code: {exit_code}
        """)

    # Additional metrics in columns
    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric("Exit Code", exit_code, delta=None)

    with col2:
        st.metric("Duration", f"{execution_time:.2f}s", delta=None)

    with col3:
        status = "PASSED" if exit_code == 0 else "FAILED"
        st.metric("Status", status, delta=None)


def render_execution_metrics_header():
    """
    Render the execution metrics header.
    """
    st.markdown("### 📈 Performance Metrics")


def render_junit_metrics_grid(xml_results):
    """
    Render JUnit XML metrics in a responsive grid layout.

    Args:
        xml_results: Parsed JUnit XML results
    """
    if not xml_results:
        st.info("No JUnit metrics available.")
        return

    # Extract metrics
    tests_run = xml_results.get('tests', 0)
    failures = xml_results.get('failures', 0)
    errors = xml_results.get('errors', 0)
    skipped = xml_results.get('skipped', 0)
    time_taken = xml_results.get('time', 0.0)

    # Calculate success rate
    if tests_run > 0:
        success_rate = ((tests_run - failures - errors) / tests_run) * 100
    else:
        success_rate = 0

    # Metrics grid
    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric("Tests Run", tests_run)
        st.metric("Success Rate", f"{success_rate:.1f}%")

    with col2:
        st.metric("Failures", failures, delta=None if failures == 0 else f"-{failures}")
        st.metric("Errors", errors, delta=None if errors == 0 else f"-{errors}")

    with col3:
        st.metric("Skipped", skipped)
        st.metric("Total Time", f"{time_taken:.2f}s")


def render_execution_output_section(test_results, verbose_mode=False):
    """
    Render the execution output section with stdout/stderr.

    Args:
        test_results: Test execution results
        verbose_mode: Whether to show verbose output
    """
    if not test_results:
        return

    stdout = test_results.get('stdout', '')
    stderr = test_results.get('stderr', '')

    if verbose_mode or stderr:
        st.markdown("### 📝 Execution Output")

        if stdout:
            with st.expander("📤 Standard Output", expanded=verbose_mode):
                st.code(stdout, language='text')

        if stderr:
            with st.expander("⚠️ Error Output", expanded=True):
                st.code(stderr, language='text')
    elif stdout:
        with st.expander("📤 Execution Output", expanded=False):
            st.code(stdout, language='text')


def render_execution_artifacts_section(test_results, verbose_mode=False):
    """
    Render the execution artifacts section with screenshots and other artifacts.

    Args:
        test_results: Test execution results
        verbose_mode: Whether to show verbose artifacts
    """
    if not test_results:
        return

    artifacts = test_results.get('artifacts', {})
    if not artifacts:
        if verbose_mode:
            st.info("No artifacts generated during execution.")
        return

    st.markdown("### 📎 Execution Artifacts")

    # Screenshots
    screenshots = artifacts.get('screenshots', [])
    if screenshots:
        with st.expander("📸 Screenshots", expanded=True):
            for i, screenshot_path in enumerate(screenshots):
                try:
                    st.image(screenshot_path, caption=f"Screenshot {i+1}")
                except Exception as e:
                    st.error(f"Failed to load screenshot {i+1}: {e}")

    # Other artifacts
    other_artifacts = {k: v for k, v in artifacts.items() if k != 'screenshots'}
    if other_artifacts and verbose_mode:
        with st.expander("📄 Other Artifacts", expanded=False):
            for artifact_type, artifact_data in other_artifacts.items():
                st.markdown(f"**{artifact_type.title()}:**")
                if isinstance(artifact_data, str):
                    st.code(artifact_data, language='text')
                else:
                    st.json(artifact_data)
